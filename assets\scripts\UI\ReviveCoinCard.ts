import { _decorator, Component, Node, Button, Label } from 'cc';
import { ItemManager, ItemType } from '../ItemManager';
import { GameData } from '../GameData';
const { ccclass, property } = _decorator;

/**
 * 复活币UI控制器
 * 负责处理复活币的购买、显示等UI逻辑
 */
@ccclass('ReviveCoinCard')
export class ReviveCoinCard extends Component {

    @property(Button)
    purchaseButton: Button = null;

    @property(Label)
    priceLabel: Label = null;

    @property(Label)
    numberLabel: Label = null;

    @property(Node)
    coinIcon: Node = null;

    @property(Node)
    coinLackSprite: Node = null; // 金币不足提示节点

    start() {
        this.initializeUI();
        this.setupButtonEvents();
        this.updateDisplay();
    }

    onEnable() {
        // 每次节点激活时刷新显示状态
        this.scheduleOnce(() => {
            this.updateDisplay();
        }, 0.1);
    }

    onDestroy() {
        // 移除事件监听，防止内存泄漏
        if (this.purchaseButton && this.purchaseButton.node && this.purchaseButton.node.isValid) {
            this.purchaseButton.node.off(Button.EventType.CLICK, this.onPurchaseClick, this);
        }
    }

    /**
     * 初始化UI
     */
    private initializeUI(): void {
        // 设置价格标签
        if (this.priceLabel) {
            const price = ItemManager.getItemPrice(ItemType.REVIVE_COIN);
            this.priceLabel.string = price.toString();
        }

        // 初始化数量标签
        if (this.numberLabel) {
            const count = ItemManager.getItemCount(ItemType.REVIVE_COIN);
            this.numberLabel.string = count.toString();
        }

        // 确保金币不足提示初始隐藏
        if (this.coinLackSprite) {
            this.coinLackSprite.active = false;
        }

        console.log("ReviveCoinCard UI 初始化完成");
    }

    /**
     * 设置按钮事件
     */
    private setupButtonEvents(): void {
        // 购买按钮事件
        if (this.purchaseButton) {
            this.purchaseButton.node.on(Button.EventType.CLICK, this.onPurchaseClick, this);
        }
    }

    /**
     * 购买按钮点击事件
     */
    private onPurchaseClick(): void {
        console.log("=== 点击购买复活币 ===");

        // 防止重复点击
        if (this.purchaseButton) {
            this.purchaseButton.interactable = false;
        }

        const price = ItemManager.getItemPrice(ItemType.REVIVE_COIN);
        const totalCoins = GameData.getTotalCoins();

        console.log(`当前金币: ${totalCoins}, 需要金币: ${price}`);

        if (totalCoins >= price) {
            console.log("金币足够，尝试购买...");
            const success = ItemManager.purchaseItem(ItemType.REVIVE_COIN);
            if (success) {
                this.updateDisplay();
                console.log("购买成功！");
            } else {
                console.log("购买失败");
            }
        } else {
            console.log("金币不足，显示提示");
            this.showCoinLackSprite();
        }

        // 延迟恢复按钮交互，防止重复点击
        this.scheduleOnce(() => {
            if (this.purchaseButton) {
                this.purchaseButton.interactable = true;
            }
        }, 0.5);
    }

    /**
     * 显示金币不足提示
     */
    private showCoinLackSprite(): void {
        if (this.coinLackSprite) {
            this.coinLackSprite.active = true;
            console.log("显示金币不足提示");

            // 2秒后自动隐藏
            this.scheduleOnce(() => {
                if (this.coinLackSprite && this.coinLackSprite.isValid) {
                    this.coinLackSprite.active = false;
                    console.log("隐藏金币不足提示");
                }
            }, 2.0);
        } else {
            console.error("金币不足提示节点未设置！");
        }
    }

    /**
     * 更新显示
     */
    public updateDisplay(): void {
        // 更新数量标签
        if (this.numberLabel) {
            const count = ItemManager.getItemCount(ItemType.REVIVE_COIN);
            this.numberLabel.string = count.toString();
        }

        // 更新购买按钮状态
        if (this.purchaseButton) {
            this.purchaseButton.node.active = true;
            // 保持按钮始终可交互，让用户点击时显示金币不足提示
            this.purchaseButton.interactable = true;
        }

        console.log("ReviveCoinCard 显示已更新");
    }

    /**
     * 获取复活币数量（供外部调用）
     */
    public getReviveCoinCount(): number {
        return ItemManager.getItemCount(ItemType.REVIVE_COIN);
    }
}
