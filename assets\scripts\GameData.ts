import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

// 小鸟类型枚举
export enum BirdType {
    NORMAL = 0,    // 普通小鸟
    GOLD = 1,      // 金色小鸟
    PENGUIN = 2,   // 企鹅小鸟
    ALBATROSS = 3, // 信天翁小鸟
    WOODPECKER = 4 // 啄木鸟小鸟
}

// 游戏模式枚举
export enum GameMode {
    NORMAL_EASY = 0,     // 普通模式-轻松
    NORMAL_STANDARD = 1, // 普通模式-标准
    NORMAL_HARD = 2,     // 普通模式-困难
    CHALLENGE_WIND = 3,  // 挑战模式-大风吹
    CHALLENGE_FOG = 4,   // 挑战模式-大雾起
    CHALLENGE_SNOW = 5   // 挑战模式-大雪飘
}


export class GameData {

    // 游戏模式相关存储键
    private static readonly BESTSCORE_PREFIX:string = "BestScore_Mode_";
    private static readonly TOP_SCORES_PREFIX:string = "TopScores_Mode_";

    // 其他存储键保持不变
    private static readonly TOTAL_COINS:string = "TotalCoins";
    private static readonly SELECTED_BIRD:string = "SelectedBird";
    private static readonly PURCHASED_BACKGROUNDS:string = "PurchasedBackgrounds";
    private static readonly SELECTED_BACKGROUND:string = "SelectedBackground";

    private static _score:number = 0;
    private static _sessionCoins:number = 0; // 本局收集的金币
    private static _selectedBirdType:BirdType = BirdType.NORMAL; // 默认选择普通小鸟
    private static _currentGameMode:GameMode = GameMode.NORMAL_STANDARD; // 当前游戏模式

    // 复活相关数据
    private static _isRevived:boolean = false; // 是否是复活状态
    private static _revivedScore:number = 0; // 复活时保存的分数
    private static _revivedSessionCoins:number = 0; // 复活时保存的本局金币

    // 静态初始化块 - 初始化游戏记录系统
    static {
        console.log("GameData: 初始化游戏记录系统");
        // 检查是否是首次运行，如果是则初始化记录为0
        GameData.initializeGameRecordsIfNeeded();
    }

    public static addScore(count:number=1){
        this._score+=count;
    }

    public static getScore():number{
        return this._score;
    }

    // 设置当前游戏模式
    public static setCurrentGameMode(mode: GameMode): void {
        this._currentGameMode = mode;
        console.log(`设置当前游戏模式: ${this.getGameModeName(mode)}`);
    }

    // 获取当前游戏模式
    public static getCurrentGameMode(): GameMode {
        return this._currentGameMode;
    }

    // 获取游戏模式名称
    public static getGameModeName(mode: GameMode): string {
        switch(mode) {
            case GameMode.NORMAL_EASY: return "普通模式-轻松";
            case GameMode.NORMAL_STANDARD: return "普通模式-标准";
            case GameMode.NORMAL_HARD: return "普通模式-困难";
            case GameMode.CHALLENGE_WIND: return "挑战模式-大风吹";
            case GameMode.CHALLENGE_FOG: return "挑战模式-大雾起";
            case GameMode.CHALLENGE_SNOW: return "挑战模式-大雪飘";
            default: return "未知模式";
        }
    }

    // 根据难度和挑战模式确定游戏模式
    public static determineGameMode(difficulty: number, challengeMode: number): GameMode {
        // 如果是挑战模式
        if (challengeMode !== 0) {
            switch(challengeMode) {
                case 1: return GameMode.CHALLENGE_WIND; // 大风吹
                case 2: return GameMode.CHALLENGE_FOG;  // 大雾起
                case 3: return GameMode.CHALLENGE_SNOW; // 大雪飘
                default: return GameMode.NORMAL_STANDARD;
            }
        }

        // 普通模式根据难度确定
        switch(difficulty) {
            case 0: return GameMode.NORMAL_EASY;     // 轻松
            case 1: return GameMode.NORMAL_STANDARD; // 标准
            case 2: return GameMode.NORMAL_HARD;     // 困难
            default: return GameMode.NORMAL_STANDARD;
        }
    }

    // 获取指定模式的最高分
    public static getBestScore(mode?: GameMode): number {
        const gameMode = mode !== undefined ? mode : this._currentGameMode;
        const key = this.BESTSCORE_PREFIX + gameMode;
        let score = localStorage.getItem(key);
        if(score){
            return parseInt(score);
        }else{
            return 0;
        }
    }

    // 获取指定模式的历史前三高分
    public static getTopScores(mode?: GameMode): number[] {
        const gameMode = mode !== undefined ? mode : this._currentGameMode;
        const key = this.TOP_SCORES_PREFIX + gameMode;
        let topScoresStr = localStorage.getItem(key);
        if (topScoresStr) {
            return JSON.parse(topScoresStr);
        } else {
            return [0, 0, 0]; // 默认值为三个0
        }
    }

    // 保存分数并更新历史前三高分
    public static saveScore(mode?: GameMode){
        const gameMode = mode !== undefined ? mode : this._currentGameMode;
        let curScore = this.getScore();
        let bestScore = this.getBestScore(gameMode);

        // 保存最高分
        if(curScore > bestScore){
            const bestScoreKey = this.BESTSCORE_PREFIX + gameMode;
            localStorage.setItem(bestScoreKey, curScore.toString());
        }

        // 更新历史前三高分
        let topScores = this.getTopScores(gameMode);
        topScores.push(curScore);
        // 排序（降序）
        topScores.sort((a, b) => b - a);
        // 只保留前三名
        topScores = topScores.slice(0, 3);
        // 保存到本地存储
        const topScoresKey = this.TOP_SCORES_PREFIX + gameMode;
        localStorage.setItem(topScoresKey, JSON.stringify(topScores));

        console.log(`保存${this.getGameModeName(gameMode)}分数: ${curScore}, 最高分: ${Math.max(curScore, bestScore)}`);
    }

    // 获取当前分数在历史前三高分中的排名（0-2表示前三名，-1表示未进入前三）
    public static getCurrentRank(mode?: GameMode): number {
        const gameMode = mode !== undefined ? mode : this._currentGameMode;
        let curScore = this.getScore();
        let topScores = this.getTopScores(gameMode);

        // 创建一个临时数组，包含当前分数和历史前三高分
        let tempScores = [...topScores];
        // 检查当前分数是否已经在数组中
        let found = false;
        for (let i = 0; i < tempScores.length; i++) {
            if (tempScores[i] === curScore) {
                found = true;
                break;
            }
        }
        if (!found) {
            tempScores.push(curScore);
        }

        // 排序（降序）
        tempScores.sort((a, b) => b - a);

        // 找出当前分数在排序后数组中的位置
        const index = tempScores.indexOf(curScore);

        // 如果当前分数在前三名内，返回其排名
        if (index < 3) {
            return index; // 返回排名（0, 1, 2分别对应金、银、铜）
        }

        return -1; // 未进入前三
    }

    public static resetScore(){
        this._score = 0;
    }

    // 复活相关方法
    /**
     * 设置复活状态并保存当前游戏数据
     */
    public static setReviveState(score: number, sessionCoins: number): void {
        this._isRevived = true;
        this._revivedScore = score;
        this._revivedSessionCoins = sessionCoins;
        console.log(`设置复活状态: 分数=${score}, 本局金币=${sessionCoins}`);
    }

    /**
     * 检查是否处于复活状态
     */
    public static isRevived(): boolean {
        return this._isRevived;
    }

    /**
     * 获取复活时的分数
     */
    public static getRevivedScore(): number {
        return this._revivedScore;
    }

    /**
     * 获取复活时的本局金币数
     */
    public static getRevivedSessionCoins(): number {
        return this._revivedSessionCoins;
    }

    /**
     * 应用复活状态（恢复分数和金币）
     */
    public static applyReviveState(): void {
        if (this._isRevived) {
            this._score = this._revivedScore;
            this._sessionCoins = this._revivedSessionCoins;
            console.log(`应用复活状态: 恢复分数=${this._score}, 恢复本局金币=${this._sessionCoins}`);
        }
    }

    /**
     * 清除复活状态
     */
    public static clearReviveState(): void {
        this._isRevived = false;
        this._revivedScore = 0;
        this._revivedSessionCoins = 0;
        console.log("清除复活状态");
    }

    // 获取总金币数
    public static getTotalCoins(): number {
        let coins = localStorage.getItem(this.TOTAL_COINS);
        if (coins) {
            return parseInt(coins);
        } else {
            return 0;
        }
    }

    // 添加金币
    public static addCoin(count: number = 1) {
        this._sessionCoins += count;

        // 更新总金币数并保存到本地存储
        const totalCoins = this.getTotalCoins() + count;
        localStorage.setItem(this.TOTAL_COINS, totalCoins.toString());

        // 输出到控制台
        console.log(`收集到金币! 本局金币: ${this._sessionCoins}, 总金币: ${totalCoins}`);
    }

    // 获取本局最终金币数（应用道具效果后）
    public static getFinalSessionCoins(): number {
        // 使用全局变量来获取倍数，避免循环引用
        const multiplier = this.getCurrentCoinMultiplier();
        return this._sessionCoins * multiplier;
    }

    // 获取当前金币倍数
    public static getCurrentCoinMultiplier(): number {
        // 从localStorage直接读取激活状态，避免循环引用
        const isDoubleCoinActive = localStorage.getItem("Item_Active_0") === "true";
        return isDoubleCoinActive ? 2 : 1;
    }

    // 应用道具效果到总金币（游戏结束时调用）
    public static applyItemEffectsToTotalCoins(): void {
        const finalSessionCoins = this.getFinalSessionCoins();
        const originalSessionCoins = this._sessionCoins;

        if (finalSessionCoins !== originalSessionCoins) {
            // 有道具效果，需要补偿差额到总金币
            const bonus = finalSessionCoins - originalSessionCoins;
            const currentTotal = this.getTotalCoins();
            const newTotal = currentTotal + bonus;
            localStorage.setItem(this.TOTAL_COINS, newTotal.toString());

            console.log(`应用道具效果: 原始金币=${originalSessionCoins}, 最终金币=${finalSessionCoins}, 总金币=${newTotal}`);
        }
    }

    // 获取本局收集的金币数
    public static getSessionCoins(): number {
        return this._sessionCoins;
    }

    // 重置本局金币数（游戏开始时调用）
    public static resetSessionCoins() {
        this._sessionCoins = 0;
    }

    // 获取当前选择的小鸟类型
    public static getSelectedBirdType(): BirdType {
        const savedType = localStorage.getItem(this.SELECTED_BIRD);
        if (savedType !== null) {
            return parseInt(savedType);
        }
        return this._selectedBirdType; // 返回默认值
    }

    // 设置选择的小鸟类型
    public static setSelectedBirdType(type: BirdType) {
        this._selectedBirdType = type;
        // 保存到本地存储
        localStorage.setItem(this.SELECTED_BIRD, type.toString());
        console.log(`选择了小鸟类型: ${type}`);
    }

    // 背景相关方法
    // 获取已购买的背景列表
    public static getPurchasedBackgrounds(): number[] {
        const purchased = localStorage.getItem(this.PURCHASED_BACKGROUNDS);
        if (purchased) {
            return JSON.parse(purchased);
        } else {
            // 默认拥有草原背景（背景1）
            return [1];
        }
    }

    // 购买背景
    public static purchaseBackground(backgroundId: number, price: number): boolean {
        const totalCoins = this.getTotalCoins();
        if (totalCoins >= price) {
            // 扣除金币
            const newTotal = totalCoins - price;
            localStorage.setItem(this.TOTAL_COINS, newTotal.toString());

            // 添加到已购买列表
            const purchased = this.getPurchasedBackgrounds();
            if (!purchased.includes(backgroundId)) {
                purchased.push(backgroundId);
                localStorage.setItem(this.PURCHASED_BACKGROUNDS, JSON.stringify(purchased));
            }

            console.log(`成功购买背景 ${backgroundId}，花费 ${price} 金币，剩余 ${newTotal} 金币`);
            return true;
        } else {
            console.log(`金币不足，无法购买背景 ${backgroundId}，需要 ${price} 金币，当前只有 ${totalCoins} 金币`);
            return false;
        }
    }

    // 检查背景是否已购买
    public static isBackgroundPurchased(backgroundId: number): boolean {
        const purchased = this.getPurchasedBackgrounds();
        return purchased.includes(backgroundId);
    }

    // 获取当前选择的背景
    public static getSelectedBackground(): number {
        const selected = localStorage.getItem(this.SELECTED_BACKGROUND);
        if (selected) {
            return parseInt(selected);
        } else {
            return 1; // 默认选择草原背景
        }
    }

    // 设置选择的背景
    public static setSelectedBackground(backgroundId: number) {
        localStorage.setItem(this.SELECTED_BACKGROUND, backgroundId.toString());
        console.log(`选择了背景: ${backgroundId}`);
    }

    // 重置背景购买状态（仅用于测试）
    public static resetBackgroundPurchases() {
        localStorage.removeItem(this.PURCHASED_BACKGROUNDS);
        localStorage.removeItem(this.SELECTED_BACKGROUND);
        console.log("背景购买状态已重置，只保留草原背景");
    }

    // 初始化游戏记录（仅在首次运行时）
    private static initializeGameRecordsIfNeeded() {
        // 检查是否已经初始化过
        const initFlag = localStorage.getItem("GameRecords_Initialized");

        if (!initFlag) {
            console.log("首次运行，初始化游戏记录为0");
            // 初始化所有模式的记录为0
            for (let mode = 0; mode <= 5; mode++) {
                const bestScoreKey = this.BESTSCORE_PREFIX + mode;
                const topScoresKey = this.TOP_SCORES_PREFIX + mode;

                // 只有在记录不存在时才设置为0
                if (!localStorage.getItem(bestScoreKey)) {
                    localStorage.setItem(bestScoreKey, "0");
                }
                if (!localStorage.getItem(topScoresKey)) {
                    localStorage.setItem(topScoresKey, JSON.stringify([0, 0, 0]));
                }
            }

            // 设置初始化标志
            localStorage.setItem("GameRecords_Initialized", "true");
            console.log("游戏记录初始化完成");
        } else {
            console.log("游戏记录已存在，保持现有记录");
        }
    }

    // 重置所有游戏模式的记录（用于测试）
    public static resetAllGameRecords() {
        for (let mode = 0; mode <= 5; mode++) {
            const bestScoreKey = this.BESTSCORE_PREFIX + mode;
            const topScoresKey = this.TOP_SCORES_PREFIX + mode;

            // 将最高分设为0
            localStorage.setItem(bestScoreKey, "0");
            // 将前三高分设为[0, 0, 0]
            localStorage.setItem(topScoresKey, JSON.stringify([0, 0, 0]));
        }
        console.log("所有游戏模式的记录已重置为0");
    }

    // 完全清除游戏记录系统（包括初始化标志）
    public static clearAllGameRecords() {
        // 清除所有模式的记录
        for (let mode = 0; mode <= 5; mode++) {
            const bestScoreKey = this.BESTSCORE_PREFIX + mode;
            const topScoresKey = this.TOP_SCORES_PREFIX + mode;
            localStorage.removeItem(bestScoreKey);
            localStorage.removeItem(topScoresKey);
        }

        // 清除初始化标志
        localStorage.removeItem("GameRecords_Initialized");
        console.log("所有游戏记录已完全清除，下次启动将重新初始化");
    }

    // 获取所有游戏模式的记录（用于调试）
    public static getAllGameRecords(): {[key: string]: any} {
        const records: {[key: string]: any} = {};

        for (let mode = 0; mode <= 5; mode++) {
            const modeName = this.getGameModeName(mode as GameMode);
            records[modeName] = {
                bestScore: this.getBestScore(mode as GameMode),
                topScores: this.getTopScores(mode as GameMode)
            };
        }

        return records;
    }

    // 打印所有游戏模式的记录（用于调试）
    public static printAllGameRecords() {
        console.log("=== 所有游戏模式记录 ===");
        const records = this.getAllGameRecords();
        for (const [modeName, record] of Object.entries(records)) {
            console.log(`${modeName}: 最高分=${record.bestScore}, 前三高分=[${record.topScores.join(', ')}]`);
        }
        console.log("========================");
    }
}
